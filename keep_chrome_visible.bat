@echo off
echo ========================================
echo     自动跟进助手 - 窗口保持工具
echo ========================================
echo.
echo 此工具将确保Chrome浏览器窗口始终保持可见状态
echo 请在启动此工具后再运行自动跟进脚本
echo.
echo 按任意键开始监控...
pause > nul

:monitor_loop
echo [%time%] 检查Chrome窗口状态...

REM 检查Chrome进程是否存在
tasklist /FI "IMAGENAME eq chrome.exe" 2>NUL | find /I /N "chrome.exe">NUL
if "%ERRORLEVEL%"=="1" (
    echo Chrome未运行，启动Chrome...
    start chrome.exe --new-window --start-maximized "https://audiep.faw-vw.com/"
    timeout /t 5 > nul
)

REM 使用PowerShell恢复最小化的Chrome窗口
powershell -Command "& {Add-Type -TypeDefinition 'using System; using System.Runtime.InteropServices; public class Win32 { [DllImport(\"user32.dll\")] public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow); [DllImport(\"user32.dll\")] public static extern bool IsIconic(IntPtr hWnd); [DllImport(\"user32.dll\")] public static extern bool SetForegroundWindow(IntPtr hWnd); }'; Get-Process chrome -ErrorAction SilentlyContinue | Where-Object {$_.MainWindowTitle -ne ''} | ForEach-Object { if([Win32]::IsIconic($_.MainWindowHandle)) { [Win32]::ShowWindow($_.MainWindowHandle, 9); [Win32]::SetForegroundWindow($_.MainWindowHandle); Write-Host '恢复Chrome窗口' } }}"

REM 等待5秒后再次检查
timeout /t 5 > nul
goto monitor_loop
