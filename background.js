// 监听安装事件
chrome.runtime.onInstalled.addListener(() => {
  console.log('[自动跟进助手] 插件已安装');

  // 设置后台运行权限
  chrome.action.setBadgeText({text: 'ON'});
  chrome.action.setBadgeBackgroundColor({color: '#4CAF50'});
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete') {
    // 注入 content script
    chrome.scripting.executeScript({
      target: { tabId: tabId },
      files: ['content.js']
    }).catch(error => {
      // 忽略已注入的错误
      console.log('Content script 可能已存在:', error);
    });
  }
});

// 防止标签页被挂起
chrome.tabs.onActivated.addListener(async (activeInfo) => {
  try {
    // 检查是否是目标网站
    const tab = await chrome.tabs.get(activeInfo.tabId);
    if (tab.url && tab.url.includes('audiep.faw-vw.com')) {
      // 发送保持活跃消息
      chrome.tabs.sendMessage(activeInfo.tabId, {
        action: 'keepAlive'
      }).catch(() => {
        // 忽略错误
      });
    }
  } catch (error) {
    console.log('保持活跃失败:', error);
  }
});

// 定期检查并激活目标标签页
setInterval(async () => {
  try {
    const tabs = await chrome.tabs.query({
      url: '*://audiep.faw-vw.com/*'
    });

    for (const tab of tabs) {
      // 发送心跳消息保持脚本活跃
      chrome.tabs.sendMessage(tab.id, {
        action: 'heartbeat'
      }).catch(() => {
        // 忽略错误
      });
    }
  } catch (error) {
    console.log('心跳检查失败:', error);
  }
}, 30000); // 每30秒检查一次

// 处理消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // 记录所有收到的消息
  console.log('[自动跟进助手] 收到消息:', request);

  if (request.action === 'contentScriptLoaded') {
    console.log('[自动跟进助手] Content script 已加载:', sender.tab?.url);
  }

  if (request.action === 'contentScriptReady') {
    console.log('[自动跟进助手] Content script 准备就绪:', request.url);
  }

  // 如果是来自popup的消息
  if (request.from === 'popup') {
    console.log('[自动跟进助手] 来自popup的消息，准备转发到content script');
    
    // 确保content script已注入
    chrome.tabs.query({active: true, currentWindow: true}, async function(tabs) {
      if (!tabs[0]) {
        console.error('[自动跟进助手] 未找到活动标签页');
        sendResponse({status: 'error', error: '未找到活动标签页'});
        return;
      }

      try {
        // 先尝试注入content script
        await chrome.scripting.executeScript({
          target: {tabId: tabs[0].id},
          files: ['content.js']
        }).catch(() => {
          // 忽略已经注入的错误
          console.log('[自动跟进助手] Content script可能已经注入');
        });

        // 发送消息到content script
        chrome.tabs.sendMessage(tabs[0].id, {
          action: request.action,
          settings: request.settings
        }, function(response) {
          if (chrome.runtime.lastError) {
            console.error('[自动跟进助手] 发送消息错误:', chrome.runtime.lastError);
            sendResponse({status: 'error', error: chrome.runtime.lastError.message});
          } else {
            console.log('[自动跟进助手] Content script响应:', response);
            sendResponse(response);
          }
        });
      } catch (error) {
        console.error('[自动跟进助手] 发送消息错误:', error);
        sendResponse({status: 'error', error: error.message});
      }
    });

    return true; // 保持消息通道开放
  }
  
  // 如果是来自content script的消息
  if (sender.tab) {
    console.log('[自动跟进助手] 来自content script的消息:', request);
    // 转发到popup
    if (request.action === 'operationResult') {
      try {
        chrome.runtime.sendMessage(request);
      } catch (error) {
        console.error('[自动跟进助手] 转发消息错误:', error);
      }
    }
  }
  
  sendResponse({received: true});
  return true;
}); 