# 自动跟进助手专用运行脚本
# 此脚本确保浏览器窗口始终保持在前台和可见状态

Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;
public class WindowManager {
    [DllImport("user32.dll")]
    public static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int X, int Y, int cx, int cy, uint uFlags);
    
    [DllImport("user32.dll")]
    public static extern IntPtr FindWindow(string lpClassName, string lpWindowName);
    
    [DllImport("user32.dll")]
    public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);
    
    [DllImport("user32.dll")]
    public static extern bool SetForegroundWindow(IntPtr hWnd);
    
    [DllImport("user32.dll")]
    public static extern bool IsIconic(IntPtr hWnd);
    
    public static readonly IntPtr HWND_TOPMOST = new IntPtr(-1);
    public static readonly uint SWP_NOMOVE = 0x0002;
    public static readonly uint SWP_NOSIZE = 0x0001;
    public static readonly int SW_RESTORE = 9;
    public static readonly int SW_MAXIMIZE = 3;
}
"@

Write-Host "=== 自动跟进助手运行环境设置 ===" -ForegroundColor Green
Write-Host "正在启动Chrome浏览器..." -ForegroundColor Yellow

# 启动Chrome浏览器
$chromeProcess = Start-Process -FilePath "chrome.exe" -ArgumentList "--new-window", "--start-maximized", "https://audiep.faw-vw.com/" -PassThru

# 等待Chrome启动
Start-Sleep -Seconds 5

Write-Host "正在设置窗口属性..." -ForegroundColor Yellow

# 持续监控和维护窗口状态
$monitoringActive = $true
$checkInterval = 5 # 每5秒检查一次

Write-Host "开始监控浏览器窗口状态..." -ForegroundColor Green
Write-Host "按 Ctrl+C 停止监控" -ForegroundColor Cyan

try {
    while ($monitoringActive) {
        # 查找Chrome窗口
        $chromeWindows = Get-Process -Name "chrome" -ErrorAction SilentlyContinue | Where-Object { $_.MainWindowTitle -ne "" }
        
        foreach ($window in $chromeWindows) {
            $hwnd = $window.MainWindowHandle
            
            if ($hwnd -ne [IntPtr]::Zero) {
                # 检查窗口是否最小化
                if ([WindowManager]::IsIconic($hwnd)) {
                    Write-Host "检测到窗口最小化，正在恢复..." -ForegroundColor Red
                    [WindowManager]::ShowWindow($hwnd, [WindowManager]::SW_MAXIMIZE)
                    [WindowManager]::SetForegroundWindow($hwnd)
                }
                
                # 设置窗口为置顶
                [WindowManager]::SetWindowPos($hwnd, [WindowManager]::HWND_TOPMOST, 0, 0, 0, 0, [WindowManager]::SWP_NOMOVE -bor [WindowManager]::SWP_NOSIZE)
                
                # 确保窗口在前台
                [WindowManager]::SetForegroundWindow($hwnd)
            }
        }
        
        # 显示状态
        $timestamp = Get-Date -Format "HH:mm:ss"
        Write-Host "[$timestamp] 窗口状态检查完成" -ForegroundColor Gray
        
        Start-Sleep -Seconds $checkInterval
    }
}
catch {
    Write-Host "监控已停止" -ForegroundColor Yellow
}

Write-Host "脚本执行完成" -ForegroundColor Green
