@echo off
echo 正在设置虚拟显示器运行环境...

REM 设置窗口始终在前台
powershell -Command "Add-Type -TypeDefinition 'using System; using System.Runtime.InteropServices; public class Win32 { [DllImport(\"user32.dll\")] public static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int X, int Y, int cx, int cy, uint uFlags); [DllImport(\"user32.dll\")] public static extern IntPtr FindWindow(string lpClassName, string lpWindowName); public static readonly IntPtr HWND_TOPMOST = new IntPtr(-1); public static readonly uint SWP_NOMOVE = 0x0002; public static readonly uint SWP_NOSIZE = 0x0001; }'"

REM 启动Chrome并设置为置顶
start chrome.exe --new-window --start-maximized "https://audiep.faw-vw.com/"

timeout /t 3

REM 将Chrome窗口设置为置顶
powershell -Command "$chrome = [Win32]::FindWindow('Chrome_WidgetWin_1', $null); if($chrome -ne [IntPtr]::Zero) { [Win32]::SetWindowPos($chrome, [Win32]::HWND_TOPMOST, 0, 0, 0, 0, [Win32]::SWP_NOMOVE -bor [Win32]::SWP_NOSIZE) }"

echo Chrome已启动并设置为置顶窗口
echo 现在可以运行自动跟进脚本了
pause
