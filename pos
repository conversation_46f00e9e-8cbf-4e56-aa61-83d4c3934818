首先模拟点击 重置  按钮  提取相关数据信息 js路径； document.querySelector("#app > section > section > main > section > div > form > div:nth-child(1) > div:nth-child(4) > div > div > button:nth-child(2)")
psot 数据
首先获取cool 跟  待跟进信息  

第一次数据
请求头：
:authority
audiep.faw-vw.com
:method
GET
:path
/api/adc/v1/lead/query/today/reply?guestName=&guestPhone=&state=&provinceCode=&cityCode=&intentionSeriesId=&intentionModelId=&callState=&leadLevel=&leadGrade=&isAddWechat=&pageIndex=1&pageSize=10&touchTypeId=&channelLargeId=&channelSmallId=&actualSourceId=&sort=next_follow_time&desc=false&_t=1754223964359
:scheme
https
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate, br, zstd
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
adcroletype
2
appid
cyx
cookie
_abfpc=0a4cf191342078c37b95b22bc54225197218ecf2_2.0; cna=********************************; username=%E8%83%A5%E8%89%B3%E7%BA%A2; jwt=*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; dataType=10461001; userId=117089; CheckedUsername=P377412; CheckedCitiesData=1; CheckedAppId=cyx; roleType=10061008; appId=cyx; employeeNo=7580290119734; companyId=169; ownerCode=7580290; dealerName=%E4%B8%9C%E8%90%A5%E9%87%91%E5%A5%A5%E6%B1%BD%E8%BD%A6%E9%94%80%E5%94%AE%E6%9C%8D%E5%8A%A1%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8; salesCode=SA37042; userOrgId=252187; dealerPhone=0546-8069067; dealerAddress=%E4%B8%9C%E8%90%A5%E5%B8%82%E5%9E%A6%E5%88%A9%E5%8C%BA%E9%83%9D%E5%AE%B6%E9%95%87%E5%8C%97%E4%BA%8C%E8%B7%AF16%E5%8F%B7; afterSale=10041001
dealercode
SA37042
dealerid
166
employeeid
119734
jwt
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
ownercode
7580290
positioncode
10061008
priority
u=1, i
referer
https://audiep.faw-vw.com/
roletype
10061008
sec-ch-ua
"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
sec-ch-ua-mobile
?0
sec-ch-ua-platform
"Windows"
sec-fetch-dest
empty
sec-fetch-mode
cors
sec-fetch-site
same-origin
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
useragent
pc
usercode
P377412
userid
117089
username
%E8%83%A5%E8%89%B3%E7%BA%A2

负载：
guestName
guestPhone
state
provinceCode
cityCode
intentionSeriesId
intentionModelId
callState
leadLevel
leadGrade
isAddWechat
pageIndex
1
pageSize
10
touchTypeId
channelLargeId
channelSmallId
actualSourceId
sort
next_follow_time
desc
false
_t
1754223964359

预览：
{code: "000000", description: "SUCCESS",…}
code
: 
"000000"
data
: 
{totalCount: 5132, pageIndex: 1, pageSize: 10, totalPage: 514,…}
pageData
: 
[{leadId: 120392904, guestName: "西城李先生", createdTime: "2025-02-04 00:35:01", state: "201",…},…]
0
: 
{leadId: 120392904, guestName: "西城李先生", createdTime: "2025-02-04 00:35:01", state: "201",…}
1
: 
{leadId: 121071320, guestName: "新区赵女士", createdTime: "2025-02-12 16:17:39", state: "201",…}
2
: 
{leadId: 121514256, guestName: "广饶张先生", createdTime: "2025-02-19 14:30:01", state: "201",…}
3
: 
{leadId: 121519154, guestName: "永安王先生", createdTime: "2025-02-19 15:45:13", state: "201",…}
4
: 
{leadId: 121732020, guestName: "李先生", createdTime: "2025-02-22 19:30:47", state: "201",…}
5
: 
{leadId: 122728492, guestName: "河口李先生", createdTime: "2025-03-09 05:48:59", state: "201",…}
6
: 
{leadId: 122864232, guestName: "客户", createdTime: "2025-03-11 05:52:02", state: "201",…}
7
: 
{leadId: 122878190, guestName: "广饶冯先生", createdTime: "2025-03-11 11:26:25", state: "201",…}
8
: 
{leadId: 122993327, guestName: "广饶刘先生", createdTime: "2025-03-12 23:26:06", state: "201",…}
9
: 
{leadId: 123168486, guestName: "广饶王先生", createdTime: "2025-03-15 12:57:29", state: "201",…}
pageIndex
: 
1
pageSize
: 
10
pages
: 
514
searchCount
: 
true
totalCount
: 
5132
totalPage
: 
514
description
: 
"SUCCESS"

响应：
{
    "code": "000000",
    "description": "SUCCESS",
    "data": {
        "totalCount": 5132,
        "pageIndex": 1,
        "pageSize": 10,
        "totalPage": 514,
        "pageData": [
            {
                "leadId": 120392904,
                "guestName": "西城李先生",
                "createdTime": "2025-02-04 00:35:01",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "山东省",
                "cityName": "东营市",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "13285462009",
                "intentionSeriesName": "Q5L",
                "channelName": "网络（考核渠道）",
                "channelBig": "懂车帝",
                "channelBigId": "21838",
                "channelSmall": "网络（考核渠道）",
                "channelSmallId": "21845",
                "touchType": "垂媒平台",
                "levelName": "A(一月内)",
                "isActive": 0,
                "isRepeat": 0,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 121071320,
                "guestName": "新区赵女士",
                "createdTime": "2025-02-12 16:17:39",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "东营市",
                "cityName": "东营市",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "13864748334",
                "intentionSeriesName": "A6L Limousine",
                "channelName": "网络（考核渠道）",
                "channelBig": "易车网",
                "channelBigId": "21839",
                "channelSmall": "网络（考核渠道）",
                "channelSmallId": "21848",
                "touchType": "垂媒平台",
                "levelName": "A(一月内)",
                "isActive": 0,
                "isRepeat": 0,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 121514256,
                "guestName": "广饶张先生",
                "createdTime": "2025-02-19 14:30:01",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "",
                "cityName": "",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "15106657044",
                "intentionSeriesName": "A4L Limousine",
                "channelName": "私信-广告流量",
                "channelBig": "经销商新媒体矩阵",
                "channelBigId": "21897",
                "channelSmall": "抖音",
                "channelSmallId": "21921",
                "actualSource": "私信-广告流量",
                "touchType": "新媒体线索",
                "levelName": "A(一月内)",
                "isActive": 0,
                "isRepeat": 0,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 121519154,
                "guestName": "永安王先生",
                "createdTime": "2025-02-19 15:45:13",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "",
                "cityName": "",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "13275468890",
                "intentionSeriesName": "A4L Limousine",
                "channelName": "私信-广告流量",
                "channelBig": "经销商新媒体矩阵",
                "channelBigId": "21897",
                "channelSmall": "抖音本地通",
                "channelSmallId": "21922",
                "actualSource": "私信-广告流量",
                "touchType": "新媒体线索",
                "levelName": "A(一月内)",
                "isActive": 0,
                "isRepeat": 1,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 121732020,
                "guestName": "李先生",
                "createdTime": "2025-02-22 19:30:47",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "东营市",
                "cityName": "东营市",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "15236171857",
                "intentionSeriesName": "Q2L",
                "channelName": "网络（考核渠道）",
                "channelBig": "易车网",
                "channelBigId": "21839",
                "channelSmall": "网络（考核渠道）",
                "channelSmallId": "21848",
                "touchType": "垂媒平台",
                "levelName": "A(一月内)",
                "isActive": 0,
                "isRepeat": 0,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 122728492,
                "guestName": "河口李先生",
                "createdTime": "2025-03-09 05:48:59",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "",
                "cityName": "",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "17758756446",
                "intentionSeriesName": "A6L Limousine",
                "channelName": "私信-自然流量",
                "channelBig": "经销商新媒体矩阵",
                "channelBigId": "21897",
                "channelSmall": "抖音本地通",
                "channelSmallId": "21922",
                "actualSource": "私信-自然流量",
                "touchType": "新媒体线索",
                "levelName": "A(一月内)",
                "isActive": 1,
                "isRepeat": 0,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 122864232,
                "guestName": "客户",
                "createdTime": "2025-03-11 05:52:02",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "山东省",
                "cityName": "东营市",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "18410146889",
                "intentionSeriesName": "Q7",
                "channelName": "网络（考核渠道）",
                "channelBig": "懂车帝",
                "channelBigId": "21838",
                "channelSmall": "网络（考核渠道）",
                "channelSmallId": "21845",
                "touchType": "垂媒平台",
                "levelName": "A(一月内)",
                "isActive": 0,
                "isRepeat": 0,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 122878190,
                "guestName": "广饶冯先生",
                "createdTime": "2025-03-11 11:26:25",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "东营市",
                "cityName": "东营市",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "13562279102",
                "intentionSeriesName": "A6L Limousine",
                "channelName": "网络（考核渠道）",
                "channelBig": "易车网",
                "channelBigId": "21839",
                "channelSmall": "网络（考核渠道）",
                "channelSmallId": "21848",
                "touchType": "垂媒平台",
                "levelName": "A(一月内)",
                "isActive": 0,
                "isRepeat": 0,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 122993327,
                "guestName": "广饶刘先生",
                "createdTime": "2025-03-12 23:26:06",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "山东省",
                "cityName": "东营市",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "13780774738",
                "intentionSeriesName": "A6L Limousine",
                "channelName": "网络（考核渠道）",
                "channelBig": "汽车之家",
                "channelBigId": "21837",
                "channelSmall": "网络（考核渠道）",
                "channelSmallId": "21842",
                "touchType": "垂媒平台",
                "levelName": "A(一月内)",
                "isActive": 0,
                "isRepeat": 0,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 123168486,
                "guestName": "广饶王先生",
                "createdTime": "2025-03-15 12:57:29",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "",
                "cityName": "",
                "nextFollowTime": "2025-08-03 19:32:53",
                "guestPhone": "13054621238",
                "intentionSeriesName": "A6L Limousine",
                "channelName": "直播-自然流量",
                "channelBig": "经销商新媒体矩阵",
                "channelBigId": "21897",
                "channelSmall": "抖音",
                "channelSmallId": "21921",
                "actualSource": "直播-自然流量",
                "touchType": "新媒体线索",
                "levelName": "A(一月内)",
                "isActive": 1,
                "isRepeat": 1,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            }
        ],
        "pages": 514,
        "searchCount": true
    }
}

cookie：
username	%E8%83%A5%E8%89%B3%E7%BA%A2	audiep.faw-vw.com	/	会话	35						Medium
userOrgId	252187	audiep.faw-vw.com	/	会话	15						Medium
userId	117089	audiep.faw-vw.com	/	会话	12						Medium
salesCode	SA37042	audiep.faw-vw.com	/	会话	16						Medium
roleType	10061008	audiep.faw-vw.com	/	会话	16						Medium
ownerCode	7580290	audiep.faw-vw.com	/	会话	16						Medium
jwt	*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************	audiep.faw-vw.com	/	会话	624						Medium
employeeNo	7580290119734	audiep.faw-vw.com	/	会话	23						Medium
dealerPhone	0546-8069067	audiep.faw-vw.com	/	会话	23						Medium
dealerName	%E4%B8%9C%E8%90%A5%E9%87%91%E5%A5%A5%E6%B1%BD%E8%BD%A6%E9%94%80%E5%94%AE%E6%9C%8D%E5%8A%A1%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8	audiep.faw-vw.com	/	会话	136						Medium
dealerAddress	%E4%B8%9C%E8%90%A5%E5%B8%82%E5%9E%A6%E5%88%A9%E5%8C%BA%E9%83%9D%E5%AE%B6%E9%95%87%E5%8C%97%E4%BA%8C%E8%B7%AF16%E5%8F%B7	audiep.faw-vw.com	/	会话	132						Medium
dataType	10461001	audiep.faw-vw.com	/	会话	16						Medium
companyId	169	audiep.faw-vw.com	/	会话	12						Medium
cna	********************************	.faw-vw.com	/	2026-09-07T05:26:23.419Z	35		✓				Medium
appId	cyx	audiep.faw-vw.com	/	会话	8						Medium
afterSale	10041001	audiep.faw-vw.com	/	会话	17						Medium
_abfpc	0a4cf191342078c37b95b22bc54225197218ecf2_2.0	.faw-vw.com	/	2026-09-04T11:51:40.100Z	50		✓				Medium
CheckedUsername	P377412	audiep.faw-vw.com	/	会话	22						Medium
CheckedCitiesData	1	audiep.faw-vw.com	/	会话	18						Medium
CheckedAppId	cyx	audiep.faw-vw.com	/	会话	15						Medium

第二次抓包
请求头；
:authority
audiep.faw-vw.com
:method
GET
:path
/api/adc/v1/lead/query/today/reply?guestName=&guestPhone=&state=&provinceCode=&cityCode=&intentionSeriesId=&intentionModelId=&callState=&leadLevel=&leadGrade=&isAddWechat=&pageIndex=1&pageSize=10&touchTypeId=&channelLargeId=&channelSmallId=&actualSourceId=&sort=next_follow_time&desc=false&_t=1754224365272
:scheme
https
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate, br, zstd
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
adcroletype
2
appid
cyx
cookie
_abfpc=0a4cf191342078c37b95b22bc54225197218ecf2_2.0; cna=********************************; username=%E8%83%A5%E8%89%B3%E7%BA%A2; jwt=*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; dataType=10461001; userId=117089; CheckedUsername=P377412; CheckedCitiesData=1; CheckedAppId=cyx; roleType=10061008; appId=cyx; employeeNo=7580290119734; companyId=169; ownerCode=7580290; dealerName=%E4%B8%9C%E8%90%A5%E9%87%91%E5%A5%A5%E6%B1%BD%E8%BD%A6%E9%94%80%E5%94%AE%E6%9C%8D%E5%8A%A1%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8; salesCode=SA37042; userOrgId=252187; dealerPhone=0546-8069067; dealerAddress=%E4%B8%9C%E8%90%A5%E5%B8%82%E5%9E%A6%E5%88%A9%E5%8C%BA%E9%83%9D%E5%AE%B6%E9%95%87%E5%8C%97%E4%BA%8C%E8%B7%AF16%E5%8F%B7; afterSale=10041001
dealercode
SA37042
dealerid
166
employeeid
119734
jwt
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
ownercode
7580290
positioncode
10061008
priority
u=1, i
referer
https://audiep.faw-vw.com/
roletype
10061008
sec-ch-ua
"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
sec-ch-ua-mobile
?0
sec-ch-ua-platform
"Windows"
sec-fetch-dest
empty
sec-fetch-mode
cors
sec-fetch-site
same-origin
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
useragent
pc
usercode
P377412
userid
117089
username
%E8%83%A5%E8%89%B3%E7%BA%A2

负载：
guestName
guestPhone
state
provinceCode
cityCode
intentionSeriesId
intentionModelId
callState
leadLevel
leadGrade
isAddWechat
pageIndex
1
pageSize
10
touchTypeId
channelLargeId
channelSmallId
actualSourceId
sort
next_follow_time
desc
false
_t
1754224365272

预览；
{code: "000000", description: "SUCCESS",…}
code
: 
"000000"
data
: 
{totalCount: 5132, pageIndex: 1, pageSize: 10, totalPage: 514,…}
pageData
: 
[{leadId: 120392904, guestName: "西城李先生", createdTime: "2025-02-04 00:35:01", state: "201",…},…]
0
: 
{leadId: 120392904, guestName: "西城李先生", createdTime: "2025-02-04 00:35:01", state: "201",…}
1
: 
{leadId: 121071320, guestName: "新区赵女士", createdTime: "2025-02-12 16:17:39", state: "201",…}
2
: 
{leadId: 121514256, guestName: "广饶张先生", createdTime: "2025-02-19 14:30:01", state: "201",…}
3
: 
{leadId: 121519154, guestName: "永安王先生", createdTime: "2025-02-19 15:45:13", state: "201",…}
4
: 
{leadId: 121732020, guestName: "李先生", createdTime: "2025-02-22 19:30:47", state: "201",…}
5
: 
{leadId: 122728492, guestName: "河口李先生", createdTime: "2025-03-09 05:48:59", state: "201",…}
6
: 
{leadId: 122864232, guestName: "客户", createdTime: "2025-03-11 05:52:02", state: "201",…}
7
: 
{leadId: 122878190, guestName: "广饶冯先生", createdTime: "2025-03-11 11:26:25", state: "201",…}
8
: 
{leadId: 122993327, guestName: "广饶刘先生", createdTime: "2025-03-12 23:26:06", state: "201",…}
9
: 
{leadId: 123168486, guestName: "广饶王先生", createdTime: "2025-03-15 12:57:29", state: "201",…}
pageIndex
: 
1
pageSize
: 
10
pages
: 
514
searchCount
: 
true
totalCount
: 
5132
totalPage
: 
514
description
: 
"SUCCESS"

相应：
{
    "code": "000000",
    "description": "SUCCESS",
    "data": {
        "totalCount": 5132,
        "pageIndex": 1,
        "pageSize": 10,
        "totalPage": 514,
        "pageData": [
            {
                "leadId": 120392904,
                "guestName": "西城李先生",
                "createdTime": "2025-02-04 00:35:01",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "山东省",
                "cityName": "东营市",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "13285462009",
                "intentionSeriesName": "Q5L",
                "channelName": "网络（考核渠道）",
                "channelBig": "懂车帝",
                "channelBigId": "21838",
                "channelSmall": "网络（考核渠道）",
                "channelSmallId": "21845",
                "touchType": "垂媒平台",
                "levelName": "A(一月内)",
                "isActive": 0,
                "isRepeat": 0,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 121071320,
                "guestName": "新区赵女士",
                "createdTime": "2025-02-12 16:17:39",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "东营市",
                "cityName": "东营市",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "13864748334",
                "intentionSeriesName": "A6L Limousine",
                "channelName": "网络（考核渠道）",
                "channelBig": "易车网",
                "channelBigId": "21839",
                "channelSmall": "网络（考核渠道）",
                "channelSmallId": "21848",
                "touchType": "垂媒平台",
                "levelName": "A(一月内)",
                "isActive": 0,
                "isRepeat": 0,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 121514256,
                "guestName": "广饶张先生",
                "createdTime": "2025-02-19 14:30:01",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "",
                "cityName": "",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "15106657044",
                "intentionSeriesName": "A4L Limousine",
                "channelName": "私信-广告流量",
                "channelBig": "经销商新媒体矩阵",
                "channelBigId": "21897",
                "channelSmall": "抖音",
                "channelSmallId": "21921",
                "actualSource": "私信-广告流量",
                "touchType": "新媒体线索",
                "levelName": "A(一月内)",
                "isActive": 0,
                "isRepeat": 0,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 121519154,
                "guestName": "永安王先生",
                "createdTime": "2025-02-19 15:45:13",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "",
                "cityName": "",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "13275468890",
                "intentionSeriesName": "A4L Limousine",
                "channelName": "私信-广告流量",
                "channelBig": "经销商新媒体矩阵",
                "channelBigId": "21897",
                "channelSmall": "抖音本地通",
                "channelSmallId": "21922",
                "actualSource": "私信-广告流量",
                "touchType": "新媒体线索",
                "levelName": "A(一月内)",
                "isActive": 0,
                "isRepeat": 1,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 121732020,
                "guestName": "李先生",
                "createdTime": "2025-02-22 19:30:47",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "东营市",
                "cityName": "东营市",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "15236171857",
                "intentionSeriesName": "Q2L",
                "channelName": "网络（考核渠道）",
                "channelBig": "易车网",
                "channelBigId": "21839",
                "channelSmall": "网络（考核渠道）",
                "channelSmallId": "21848",
                "touchType": "垂媒平台",
                "levelName": "A(一月内)",
                "isActive": 0,
                "isRepeat": 0,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 122728492,
                "guestName": "河口李先生",
                "createdTime": "2025-03-09 05:48:59",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "",
                "cityName": "",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "17758756446",
                "intentionSeriesName": "A6L Limousine",
                "channelName": "私信-自然流量",
                "channelBig": "经销商新媒体矩阵",
                "channelBigId": "21897",
                "channelSmall": "抖音本地通",
                "channelSmallId": "21922",
                "actualSource": "私信-自然流量",
                "touchType": "新媒体线索",
                "levelName": "A(一月内)",
                "isActive": 1,
                "isRepeat": 0,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 122864232,
                "guestName": "客户",
                "createdTime": "2025-03-11 05:52:02",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "山东省",
                "cityName": "东营市",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "18410146889",
                "intentionSeriesName": "Q7",
                "channelName": "网络（考核渠道）",
                "channelBig": "懂车帝",
                "channelBigId": "21838",
                "channelSmall": "网络（考核渠道）",
                "channelSmallId": "21845",
                "touchType": "垂媒平台",
                "levelName": "A(一月内)",
                "isActive": 0,
                "isRepeat": 0,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 122878190,
                "guestName": "广饶冯先生",
                "createdTime": "2025-03-11 11:26:25",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "东营市",
                "cityName": "东营市",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "13562279102",
                "intentionSeriesName": "A6L Limousine",
                "channelName": "网络（考核渠道）",
                "channelBig": "易车网",
                "channelBigId": "21839",
                "channelSmall": "网络（考核渠道）",
                "channelSmallId": "21848",
                "touchType": "垂媒平台",
                "levelName": "A(一月内)",
                "isActive": 0,
                "isRepeat": 0,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 122993327,
                "guestName": "广饶刘先生",
                "createdTime": "2025-03-12 23:26:06",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "山东省",
                "cityName": "东营市",
                "nextFollowTime": "2025-08-03 19:32:52",
                "guestPhone": "13780774738",
                "intentionSeriesName": "A6L Limousine",
                "channelName": "网络（考核渠道）",
                "channelBig": "汽车之家",
                "channelBigId": "21837",
                "channelSmall": "网络（考核渠道）",
                "channelSmallId": "21842",
                "touchType": "垂媒平台",
                "levelName": "A(一月内)",
                "isActive": 0,
                "isRepeat": 0,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            },
            {
                "leadId": 123168486,
                "guestName": "广饶王先生",
                "createdTime": "2025-03-15 12:57:29",
                "state": "201",
                "stateName": "再次待跟进",
                "provinceName": "",
                "cityName": "",
                "nextFollowTime": "2025-08-03 19:32:53",
                "guestPhone": "13054621238",
                "intentionSeriesName": "A6L Limousine",
                "channelName": "直播-自然流量",
                "channelBig": "经销商新媒体矩阵",
                "channelBigId": "21897",
                "channelSmall": "抖音",
                "channelSmallId": "21921",
                "actualSource": "直播-自然流量",
                "touchType": "新媒体线索",
                "levelName": "A(一月内)",
                "isActive": 1,
                "isRepeat": 1,
                "isTimeOut": 0,
                "isSleep": 0,
                "isGrab": 0,
                "isInvalid": 0,
                "isCallout": 0,
                "isAddWechat": 0,
                "consultant": "胥艳红",
                "followLeftTime": "0小时",
                "bigState": "有效",
                "bigStateId": 0
            }
        ],
        "pages": 514,
        "searchCount": true
    }
}

cookie：
_abfpc	0a4cf191342078c37b95b22bc54225197218ecf2_2.0	.faw-vw.com	/	2026-09-04T11:51:40.100Z	50		✓				Medium
afterSale	10041001	audiep.faw-vw.com	/	会话	17						Medium
appId	cyx	audiep.faw-vw.com	/	会话	8						Medium
CheckedAppId	cyx	audiep.faw-vw.com	/	会话	15						Medium
CheckedCitiesData	1	audiep.faw-vw.com	/	会话	18						Medium
CheckedUsername	P377412	audiep.faw-vw.com	/	会话	22						Medium
cna	********************************	.faw-vw.com	/	2026-09-07T05:26:23.419Z	35		✓				Medium
companyId	169	audiep.faw-vw.com	/	会话	12						Medium
dataType	10461001	audiep.faw-vw.com	/	会话	16						Medium
dealerAddress	%E4%B8%9C%E8%90%A5%E5%B8%82%E5%9E%A6%E5%88%A9%E5%8C%BA%E9%83%9D%E5%AE%B6%E9%95%87%E5%8C%97%E4%BA%8C%E8%B7%AF16%E5%8F%B7	audiep.faw-vw.com	/	会话	132						Medium
dealerName	%E4%B8%9C%E8%90%A5%E9%87%91%E5%A5%A5%E6%B1%BD%E8%BD%A6%E9%94%80%E5%94%AE%E6%9C%8D%E5%8A%A1%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8	audiep.faw-vw.com	/	会话	136						Medium
dealerPhone	0546-8069067	audiep.faw-vw.com	/	会话	23						Medium
employeeNo	7580290119734	audiep.faw-vw.com	/	会话	23						Medium
jwt	*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************	audiep.faw-vw.com	/	会话	624						Medium
ownerCode	7580290	audiep.faw-vw.com	/	会话	16						Medium
roleType	10061008	audiep.faw-vw.com	/	会话	16						Medium
salesCode	SA37042	audiep.faw-vw.com	/	会话	16						Medium
userId	117089	audiep.faw-vw.com	/	会话	12						Medium
username	%E8%83%A5%E8%89%B3%E7%BA%A2	audiep.faw-vw.com	/	会话	35						Medium
userOrgId	252187	audiep.faw-vw.com	/	会话	15						Medium


点击跟进 第一次抓包数据：
头：
:authority
audiep.faw-vw.com
:method
GET
:path
/api/adc/v1/lead/queryLeadDetail?leadId=120392904&_t=1754224625869
:scheme
https
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate, br, zstd
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
appid
cyx
cookie
_abfpc=0a4cf191342078c37b95b22bc54225197218ecf2_2.0; cna=********************************; username=%E8%83%A5%E8%89%B3%E7%BA%A2; jwt=*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; dataType=10461001; userId=117089; CheckedUsername=P377412; CheckedCitiesData=1; CheckedAppId=cyx; roleType=10061008; appId=cyx; employeeNo=7580290119734; companyId=169; ownerCode=7580290; dealerName=%E4%B8%9C%E8%90%A5%E9%87%91%E5%A5%A5%E6%B1%BD%E8%BD%A6%E9%94%80%E5%94%AE%E6%9C%8D%E5%8A%A1%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8; salesCode=SA37042; userOrgId=252187; dealerPhone=0546-8069067; dealerAddress=%E4%B8%9C%E8%90%A5%E5%B8%82%E5%9E%A6%E5%88%A9%E5%8C%BA%E9%83%9D%E5%AE%B6%E9%95%87%E5%8C%97%E4%BA%8C%E8%B7%AF16%E5%8F%B7; afterSale=10041001
jwt
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
ownercode
7580290
priority
u=1, i
referer
https://audiep.faw-vw.com/
roletype
10061008
sec-ch-ua
"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
sec-ch-ua-mobile
?0
sec-ch-ua-platform
"Windows"
sec-fetch-dest
empty
sec-fetch-mode
cors
sec-fetch-site
same-origin
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
useragent
pc
userid
117089

负载：
leadId
120392904
_t
1754224625869

预览：
{code: "000000", description: "SUCCESS",…}
code
: 
"000000"
data
: 
{vehicleColor: "", dealerName: "东营金奥汽车销售服务有限公司", isTestDrive: 0, userSex: 0, isSleep: 0,…}
bigState
: 
"有效"
bigStateId
: 
0
callState
: 
-1
channelBig
: 
"懂车帝"
channelBigId
: 
"21838"
channelSmall
: 
"网络（考核渠道）"
channelSmallId
: 
"21845"
cityCode
: 
"370500"
cityName
: 
"东营市"
clueCollectionToolsId
: 
""
consultant
: 
"胥艳红"
consultantId
: 
"117089"
createdTime
: 
"2025-02-04 00:35:01"
dealerName
: 
"东营金奥汽车销售服务有限公司"
extraData
: 
""
followMethod
: 
"电话沟通"
handDealerTime
: 
"2025-02-04 00:35:01"
intentionModelId
: 
""
intentionSeriesId
: 
"1020"
intentionSeriesName
: 
"Q5L"
inviterType
: 
"ADC"
inviterTypeId
: 
"10011002"
isGrab
: 
0
isInvalid
: 
0
isSleep
: 
0
isTestDrive
: 
0
leadId
: 
120392904
levelId
: 
1
levelName
: 
"A(一月内)"
nextFollowTime
: 
"2025-08-03 19:32:52"
otherContact
: 
"13954622060"
provinceCode
: 
"370000"
provinceName
: 
"山东省"
recomenderPhone
: 
"0"
remark
: 
"客户咨询A4L40时尚 分期报价178000左右 客户说时间进店看车 加手机号微信 发位置了 再联系"
state
: 
201
stateName
: 
"再次待跟进"
touchType
: 
"垂媒平台"
touchTypeId
: 
"21836"
userAddress
: 
""
userEmail
: 
""
userMobile
: 
"13285462009"
userName
: 
"西城李先生"
userQq
: 
""
userSex
: 
0
userTag
: 
""
userWeChat
: 
""
vehicleColor
: 
""
description
: 
"SUCCESS"

响应：
{
    "code": "000000",
    "description": "SUCCESS",
    "data": {
        "vehicleColor": "",
        "dealerName": "东营金奥汽车销售服务有限公司",
        "isTestDrive": 0,
        "userSex": 0,
        "isSleep": 0,
        "remark": "客户咨询A4L40时尚 分期报价178000左右 客户说时间进店看车 加手机号微信 发位置了 再联系",
        "state": 201,
        "stateName": "再次待跟进",
        "bigState": "有效",
        "leadId": 120392904,
        "levelId": 1,
        "levelName": "A(一月内)",
        "userName": "西城李先生",
        "createdTime": "2025-02-04 00:35:01",
        "provinceCode": "370000",
        "provinceName": "山东省",
        "cityCode": "370500",
        "cityName": "东营市",
        "nextFollowTime": "2025-08-03 19:32:52",
        "userMobile": "13285462009",
        "intentionModelId": "",
        "intentionSeriesId": "1020",
        "intentionSeriesName": "Q5L",
        "otherContact": "13954622060",
        "userAddress": "",
        "userQq": "",
        "userEmail": "",
        "userWeChat": "",
        "userTag": "",
        "callState": -1,
        "handDealerTime": "2025-02-04 00:35:01",
        "touchType": "垂媒平台",
        "touchTypeId": "21836",
        "channelBig": "懂车帝",
        "channelBigId": "21838",
        "channelSmall": "网络（考核渠道）",
        "channelSmallId": "21845",
        "consultantId": "117089",
        "consultant": "胥艳红",
        "isInvalid": 0,
        "recomenderPhone": "0",
        "isGrab": 0,
        "inviterTypeId": "10011002",
        "inviterType": "ADC",
        "clueCollectionToolsId": "",
        "extraData": "",
        "followMethod": "电话沟通",
        "bigStateId": 0
    }
}

cookie：
_abfpc	0a4cf191342078c37b95b22bc54225197218ecf2_2.0	.faw-vw.com	/	2026-09-04T11:51:40.100Z	50		✓				Medium
afterSale	10041001	audiep.faw-vw.com	/	会话	17						Medium
appId	cyx	audiep.faw-vw.com	/	会话	8						Medium
CheckedAppId	cyx	audiep.faw-vw.com	/	会话	15						Medium
CheckedCitiesData	1	audiep.faw-vw.com	/	会话	18						Medium
CheckedUsername	P377412	audiep.faw-vw.com	/	会话	22						Medium
cna	********************************	.faw-vw.com	/	2026-09-07T05:26:23.419Z	35		✓				Medium
companyId	169	audiep.faw-vw.com	/	会话	12						Medium
dataType	10461001	audiep.faw-vw.com	/	会话	16						Medium
dealerAddress	%E4%B8%9C%E8%90%A5%E5%B8%82%E5%9E%A6%E5%88%A9%E5%8C%BA%E9%83%9D%E5%AE%B6%E9%95%87%E5%8C%97%E4%BA%8C%E8%B7%AF16%E5%8F%B7	audiep.faw-vw.com	/	会话	132						Medium
dealerName	%E4%B8%9C%E8%90%A5%E9%87%91%E5%A5%A5%E6%B1%BD%E8%BD%A6%E9%94%80%E5%94%AE%E6%9C%8D%E5%8A%A1%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8	audiep.faw-vw.com	/	会话	136						Medium
dealerPhone	0546-8069067	audiep.faw-vw.com	/	会话	23						Medium
employeeNo	7580290119734	audiep.faw-vw.com	/	会话	23						Medium
jwt	*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************	audiep.faw-vw.com	/	会话	624						Medium
ownerCode	7580290	audiep.faw-vw.com	/	会话	16						Medium
roleType	10061008	audiep.faw-vw.com	/	会话	16						Medium
salesCode	SA37042	audiep.faw-vw.com	/	会话	16						Medium
userId	117089	audiep.faw-vw.com	/	会话	12						Medium
username	%E8%83%A5%E8%89%B3%E7%BA%A2	audiep.faw-vw.com	/	会话	35						Medium
userOrgId	252187	audiep.faw-vw.com	/	会话	15						Medium

点击提交
头：
:authority
audiep.faw-vw.com
:method
POST
:path
/api/adc/v1/lead/followUp?_t=1754224995788
:scheme
https
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate, br, zstd
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
adcroletype
2
appid
cyx
content-length
986
content-type
application/json
cookie
_abfpc=0a4cf191342078c37b95b22bc54225197218ecf2_2.0; cna=********************************; username=%E8%83%A5%E8%89%B3%E7%BA%A2; jwt=*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; dataType=10461001; userId=117089; CheckedUsername=P377412; CheckedCitiesData=1; CheckedAppId=cyx; roleType=10061008; appId=cyx; employeeNo=7580290119734; companyId=169; ownerCode=7580290; dealerName=%E4%B8%9C%E8%90%A5%E9%87%91%E5%A5%A5%E6%B1%BD%E8%BD%A6%E9%94%80%E5%94%AE%E6%9C%8D%E5%8A%A1%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8; salesCode=SA37042; userOrgId=252187; dealerPhone=0546-8069067; dealerAddress=%E4%B8%9C%E8%90%A5%E5%B8%82%E5%9E%A6%E5%88%A9%E5%8C%BA%E9%83%9D%E5%AE%B6%E9%95%87%E5%8C%97%E4%BA%8C%E8%B7%AF16%E5%8F%B7; afterSale=10041001
dealercode
SA37042
dealerid
166
employeeid
119734
jwt
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
origin
https://audiep.faw-vw.com
ownercode
7580290
positioncode
10061008
priority
u=1, i
referer
https://audiep.faw-vw.com/
roletype
10061008
sec-ch-ua
"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
sec-ch-ua-mobile
?0
sec-ch-ua-platform
"Windows"
sec-fetch-dest
empty
sec-fetch-mode
cors
sec-fetch-site
same-origin
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
useragent
pc
usercode
P377412
userid
117089
username
%E8%83%A5%E8%89%B3%E7%BA%A2

负载：

_t
1754224995788
{leadId: 120392904, userName: "西城李先生", userMobile: "13285462009", intentionSeries: "1020_Q5L",…}
actualBuyer
: 
""
actualBuyerPhone
: 
""
bigState
: 
"有效"
bigStateId
: 
0
buyCarDate
: 
"2025-08-03 20:42:44"
competition
: 
""
consultant
: 
"胥艳红"
consultantId
: 
"117089"
currentCarFocus
: 
""
customerFocus
: 
""
failReason
: 
""
followMethod
: 
"电话沟通"
intentionModel
: 
""
intentionModelId
: 
""
intentionSeries
: 
"1020_Q5L"
intentionSeriesId
: 
"1020"
intentionSeriesName
: 
"Q5L"
invalidReason
: 
""
isFinancial
: 
""
isInvalid
: 
0
isSleep
: 
""
isTestDrive
: 
""
leadId
: 
120392904
level
: 
"2_B（30天内跟进）_720"
levelId
: 
"2"
levelName
: 
"B（30天内跟进）"
nextFollowTime
: 
"2025-09-02 23:59:00"
nextState
: 
201
policyFocus
: 
""
purchaseBudget
: 
""
purchaseType
: 
""
recomender
: 
""
recomenderChassisNo
: 
""
recomenderLicensePlate
: 
""
recomenderPhone
: 
""
recommenderRegisteredTime
: 
""
remark
: 
"内容123"
replacementType
: 
1
state
: 
201
stateName
: 
"再次待跟进"
userMobile
: 
"13285462009"
userName
: 
"西城李先生"
vehicleChassisNo
: 
""
vehicleConfig
: 
""

预览：
{code: "000000", description: "SUCCESS", data: "SUCCESS"}
code
: 
"000000"
data
: 
"SUCCESS"
description
: 
"SUCCESS"

响应：
{"code":"000000","description":"SUCCESS","data":"SUCCESS"}

cookie：_abfpc	0a4cf191342078c37b95b22bc54225197218ecf2_2.0	.faw-vw.com	/	2026-09-04T11:51:40.100Z	50		✓				Medium
afterSale	10041001	audiep.faw-vw.com	/	会话	17						Medium
appId	cyx	audiep.faw-vw.com	/	会话	8						Medium
CheckedAppId	cyx	audiep.faw-vw.com	/	会话	15						Medium
CheckedCitiesData	1	audiep.faw-vw.com	/	会话	18						Medium
CheckedUsername	P377412	audiep.faw-vw.com	/	会话	22						Medium
cna	********************************	.faw-vw.com	/	2026-09-07T05:26:23.419Z	35		✓				Medium
companyId	169	audiep.faw-vw.com	/	会话	12						Medium
dataType	10461001	audiep.faw-vw.com	/	会话	16						Medium
dealerAddress	%E4%B8%9C%E8%90%A5%E5%B8%82%E5%9E%A6%E5%88%A9%E5%8C%BA%E9%83%9D%E5%AE%B6%E9%95%87%E5%8C%97%E4%BA%8C%E8%B7%AF16%E5%8F%B7	audiep.faw-vw.com	/	会话	132						Medium
dealerName	%E4%B8%9C%E8%90%A5%E9%87%91%E5%A5%A5%E6%B1%BD%E8%BD%A6%E9%94%80%E5%94%AE%E6%9C%8D%E5%8A%A1%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8	audiep.faw-vw.com	/	会话	136						Medium
dealerPhone	0546-8069067	audiep.faw-vw.com	/	会话	23						Medium
employeeNo	7580290119734	audiep.faw-vw.com	/	会话	23						Medium
jwt	*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************	audiep.faw-vw.com	/	会话	624						Medium
ownerCode	7580290	audiep.faw-vw.com	/	会话	16						Medium
roleType	10061008	audiep.faw-vw.com	/	会话	16						Medium
salesCode	SA37042	audiep.faw-vw.com	/	会话	16						Medium
userId	117089	audiep.faw-vw.com	/	会话	12						Medium
username	%E8%83%A5%E8%89%B3%E7%BA%A2	audiep.faw-vw.com	/	会话	35						Medium
userOrgId	252187	audiep.faw-vw.com	/	会话	15						Medium

第二次 点击跟进按钮
头：
authority
audiep.faw-vw.com
:method
GET
:path
/api/adc/v1/lead/queryLeadDetail?leadId=121071320&_t=1754225174308
:scheme
https
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate, br, zstd
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
appid
cyx
cookie
_abfpc=0a4cf191342078c37b95b22bc54225197218ecf2_2.0; cna=********************************; username=%E8%83%A5%E8%89%B3%E7%BA%A2; jwt=*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; dataType=10461001; userId=117089; CheckedUsername=P377412; CheckedCitiesData=1; CheckedAppId=cyx; roleType=10061008; appId=cyx; employeeNo=7580290119734; companyId=169; ownerCode=7580290; dealerName=%E4%B8%9C%E8%90%A5%E9%87%91%E5%A5%A5%E6%B1%BD%E8%BD%A6%E9%94%80%E5%94%AE%E6%9C%8D%E5%8A%A1%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8; salesCode=SA37042; userOrgId=252187; dealerPhone=0546-8069067; dealerAddress=%E4%B8%9C%E8%90%A5%E5%B8%82%E5%9E%A6%E5%88%A9%E5%8C%BA%E9%83%9D%E5%AE%B6%E9%95%87%E5%8C%97%E4%BA%8C%E8%B7%AF16%E5%8F%B7; afterSale=10041001
jwt
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
ownercode
7580290
priority
u=1, i
referer
https://audiep.faw-vw.com/
roletype
10061008
sec-ch-ua
"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
sec-ch-ua-mobile
?0
sec-ch-ua-platform
"Windows"
sec-fetch-dest
empty
sec-fetch-mode
cors
sec-fetch-site
same-origin
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
useragent
pc
userid
117089

负载：

leadId
121071320
_t
1754225174308

预览：
{code: "000000", description: "SUCCESS",…}
code
: 
"000000"
data
: 
{vehicleColor: "", dealerName: "东营金奥汽车销售服务有限公司", isTestDrive: 0, userSex: 1, isSleep: 0,…}
bigState
: 
"有效"
bigStateId
: 
0
callState
: 
-1
channelBig
: 
"易车网"
channelBigId
: 
"21839"
channelSmall
: 
"网络（考核渠道）"
channelSmallId
: 
"21848"
cityCode
: 
"370500"
cityName
: 
"东营市"
clueCollectionToolsId
: 
""
consultant
: 
"胥艳红"
consultantId
: 
"117089"
createdTime
: 
"2025-02-12 16:17:39"
dealerName
: 
"东营金奥汽车销售服务有限公司"
extraData
: 
""
followMethod
: 
"电话沟通"
handDealerTime
: 
"2025-02-12 16:17:39"
intentionModelId
: 
""
intentionSeriesId
: 
"1011"
intentionSeriesName
: 
"A6L Limousine"
inviterType
: 
"ADC"
inviterTypeId
: 
"10011002"
isGrab
: 
0
isInvalid
: 
0
isSleep
: 
0
isTestDrive
: 
0
leadId
: 
121071320
levelId
: 
1
levelName
: 
"A(一月内)"
nextFollowTime
: 
"2025-08-03 19:32:52"
provinceCode
: 
"370500"
provinceName
: 
"东营市"
recomenderPhone
: 
"0"
remark
: 
"客户咨询A6L40豪华 分期报价276000 客户说尽量抽时间进店看车 加手机号微信了 发位置了 再联系"
state
: 
201
stateName
: 
"再次待跟进"
touchType
: 
"垂媒平台"
touchTypeId
: 
"21836"
userAddress
: 
""
userEmail
: 
""
userMobile
: 
"13864748334"
userName
: 
"新区赵女士"
userQq
: 
""
userSex
: 
1
userTag
: 
""
userWeChat
: 
""
vehicleColor
: 
""
description
: 
"SUCCESS"

响应：
{
    "code": "000000",
    "description": "SUCCESS",
    "data": {
        "vehicleColor": "",
        "dealerName": "东营金奥汽车销售服务有限公司",
        "isTestDrive": 0,
        "userSex": 1,
        "isSleep": 0,
        "remark": "客户咨询A6L40豪华 分期报价276000 客户说尽量抽时间进店看车 加手机号微信了 发位置了 再联系",
        "state": 201,
        "stateName": "再次待跟进",
        "bigState": "有效",
        "leadId": 121071320,
        "levelId": 1,
        "levelName": "A(一月内)",
        "userName": "新区赵女士",
        "createdTime": "2025-02-12 16:17:39",
        "provinceCode": "370500",
        "provinceName": "东营市",
        "cityCode": "370500",
        "cityName": "东营市",
        "nextFollowTime": "2025-08-03 19:32:52",
        "userMobile": "13864748334",
        "intentionModelId": "",
        "intentionSeriesId": "1011",
        "intentionSeriesName": "A6L Limousine",
        "userAddress": "",
        "userQq": "",
        "userEmail": "",
        "userWeChat": "",
        "userTag": "",
        "callState": -1,
        "handDealerTime": "2025-02-12 16:17:39",
        "touchType": "垂媒平台",
        "touchTypeId": "21836",
        "channelBig": "易车网",
        "channelBigId": "21839",
        "channelSmall": "网络（考核渠道）",
        "channelSmallId": "21848",
        "consultantId": "117089",
        "consultant": "胥艳红",
        "isInvalid": 0,
        "recomenderPhone": "0",
        "isGrab": 0,
        "inviterTypeId": "10011002",
        "inviterType": "ADC",
        "clueCollectionToolsId": "",
        "extraData": "",
        "followMethod": "电话沟通",
        "bigStateId": 0
    }
}

cookie：
_abfpc	0a4cf191342078c37b95b22bc54225197218ecf2_2.0	.faw-vw.com	/	2026-09-04T11:51:40.100Z	50		✓				Medium
afterSale	10041001	audiep.faw-vw.com	/	会话	17						Medium
appId	cyx	audiep.faw-vw.com	/	会话	8						Medium
CheckedAppId	cyx	audiep.faw-vw.com	/	会话	15						Medium
CheckedCitiesData	1	audiep.faw-vw.com	/	会话	18						Medium
CheckedUsername	P377412	audiep.faw-vw.com	/	会话	22						Medium
cna	********************************	.faw-vw.com	/	2026-09-07T05:26:23.419Z	35		✓				Medium
companyId	169	audiep.faw-vw.com	/	会话	12						Medium
dataType	10461001	audiep.faw-vw.com	/	会话	16						Medium
dealerAddress	%E4%B8%9C%E8%90%A5%E5%B8%82%E5%9E%A6%E5%88%A9%E5%8C%BA%E9%83%9D%E5%AE%B6%E9%95%87%E5%8C%97%E4%BA%8C%E8%B7%AF16%E5%8F%B7	audiep.faw-vw.com	/	会话	132						Medium
dealerName	%E4%B8%9C%E8%90%A5%E9%87%91%E5%A5%A5%E6%B1%BD%E8%BD%A6%E9%94%80%E5%94%AE%E6%9C%8D%E5%8A%A1%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8	audiep.faw-vw.com	/	会话	136						Medium
dealerPhone	0546-8069067	audiep.faw-vw.com	/	会话	23						Medium
employeeNo	7580290119734	audiep.faw-vw.com	/	会话	23						Medium
jwt	*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************	audiep.faw-vw.com	/	会话	624						Medium
ownerCode	7580290	audiep.faw-vw.com	/	会话	16						Medium
roleType	10061008	audiep.faw-vw.com	/	会话	16						Medium
salesCode	SA37042	audiep.faw-vw.com	/	会话	16						Medium
userId	117089	audiep.faw-vw.com	/	会话	12						Medium
username	%E8%83%A5%E8%89%B3%E7%BA%A2	audiep.faw-vw.com	/	会话	35						Medium
userOrgId	252187	audiep.faw-vw.com	/	会话	15						Medium

提交
头：
:authority
audiep.faw-vw.com
:method
POST
:path
/api/adc/v1/lead/followUp?_t=1754225356385
:scheme
https
accept
application/json, text/plain, */*
accept-encoding
gzip, deflate, br, zstd
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
adcroletype
2
appid
cyx
content-length
1008
content-type
application/json
cookie
_abfpc=0a4cf191342078c37b95b22bc54225197218ecf2_2.0; cna=********************************; username=%E8%83%A5%E8%89%B3%E7%BA%A2; jwt=*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; dataType=10461001; userId=117089; CheckedUsername=P377412; CheckedCitiesData=1; CheckedAppId=cyx; roleType=10061008; appId=cyx; employeeNo=7580290119734; companyId=169; ownerCode=7580290; dealerName=%E4%B8%9C%E8%90%A5%E9%87%91%E5%A5%A5%E6%B1%BD%E8%BD%A6%E9%94%80%E5%94%AE%E6%9C%8D%E5%8A%A1%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8; salesCode=SA37042; userOrgId=252187; dealerPhone=0546-8069067; dealerAddress=%E4%B8%9C%E8%90%A5%E5%B8%82%E5%9E%A6%E5%88%A9%E5%8C%BA%E9%83%9D%E5%AE%B6%E9%95%87%E5%8C%97%E4%BA%8C%E8%B7%AF16%E5%8F%B7; afterSale=10041001
dealercode
SA37042
dealerid
166
employeeid
119734
jwt
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
origin
https://audiep.faw-vw.com
ownercode
7580290
positioncode
10061008
priority
u=1, i
referer
https://audiep.faw-vw.com/
roletype
10061008
sec-ch-ua
"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"
sec-ch-ua-mobile
?0
sec-ch-ua-platform
"Windows"
sec-fetch-dest
empty
sec-fetch-mode
cors
sec-fetch-site
same-origin
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
useragent
pc
usercode
P377412
userid
117089
username
%E8%83%A5%E8%89%B3%E7%BA%A2

负载；
{leadId: 121071320, userName: "新区赵女士", userMobile: "13864748334",…}
actualBuyer
: 
""
actualBuyerPhone
: 
""
bigState
: 
"有效"
bigStateId
: 
0
buyCarDate
: 
"2025-08-03 20:48:52"
competition
: 
""
consultant
: 
"胥艳红"
consultantId
: 
"117089"
currentCarFocus
: 
""
customerFocus
: 
""
failReason
: 
""
followMethod
: 
"电话沟通"
intentionModel
: 
""
intentionModelId
: 
""
intentionSeries
: 
"1011_A6L Limousine"
intentionSeriesId
: 
"1011"
intentionSeriesName
: 
"A6L Limousine"
invalidReason
: 
""
isFinancial
: 
""
isInvalid
: 
0
isSleep
: 
""
isTestDrive
: 
""
leadId
: 
121071320
level
: 
"2_B（30天内跟进）_720"
levelId
: 
"2"
levelName
: 
"B（30天内跟进）"
nextFollowTime
: 
"2025-09-02 23:59:00"
nextState
: 
201
policyFocus
: 
""
purchaseBudget
: 
""
purchaseType
: 
""
recomender
: 
""
recomenderChassisNo
: 
""
recomenderLicensePlate
: 
""
recomenderPhone
: 
""
recommenderRegisteredTime
: 
""
remark
: 
"回复12345"
replacementType
: 
1
state
: 
201
stateName
: 
"再次待跟进"
userMobile
: 
"13864748334"
userName
: 
"新区赵女士"
vehicleChassisNo
: 
""
vehicleConfig
: 
""

预览：
{code: "000000", description: "SUCCESS", data: "SUCCESS"}
code
: 
"000000"
data
: 
"SUCCESS"
description
: 
"SUCCESS"

响应；
{"code":"000000","description":"SUCCESS","data":"SUCCESS"}

cookie；
_abfpc	0a4cf191342078c37b95b22bc54225197218ecf2_2.0	.faw-vw.com	/	2026-09-04T11:51:40.100Z	50		✓				Medium
afterSale	10041001	audiep.faw-vw.com	/	会话	17						Medium
appId	cyx	audiep.faw-vw.com	/	会话	8						Medium
CheckedAppId	cyx	audiep.faw-vw.com	/	会话	15						Medium
CheckedCitiesData	1	audiep.faw-vw.com	/	会话	18						Medium
CheckedUsername	P377412	audiep.faw-vw.com	/	会话	22						Medium
cna	********************************	.faw-vw.com	/	2026-09-07T05:26:23.419Z	35		✓				Medium
companyId	169	audiep.faw-vw.com	/	会话	12						Medium
dataType	10461001	audiep.faw-vw.com	/	会话	16						Medium
dealerAddress	%E4%B8%9C%E8%90%A5%E5%B8%82%E5%9E%A6%E5%88%A9%E5%8C%BA%E9%83%9D%E5%AE%B6%E9%95%87%E5%8C%97%E4%BA%8C%E8%B7%AF16%E5%8F%B7	audiep.faw-vw.com	/	会话	132						Medium
dealerName	%E4%B8%9C%E8%90%A5%E9%87%91%E5%A5%A5%E6%B1%BD%E8%BD%A6%E9%94%80%E5%94%AE%E6%9C%8D%E5%8A%A1%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8	audiep.faw-vw.com	/	会话	136						Medium
dealerPhone	0546-8069067	audiep.faw-vw.com	/	会话	23						Medium
employeeNo	7580290119734	audiep.faw-vw.com	/	会话	23						Medium
jwt	*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************	audiep.faw-vw.com	/	会话	624						Medium
ownerCode	7580290	audiep.faw-vw.com	/	会话	16						Medium
roleType	10061008	audiep.faw-vw.com	/	会话	16						Medium
salesCode	SA37042	audiep.faw-vw.com	/	会话	16						Medium
userId	117089	audiep.faw-vw.com	/	会话	12						Medium
username	%E8%83%A5%E8%89%B3%E7%BA%A2	audiep.faw-vw.com	/	会话	35						Medium
userOrgId	252187	audiep.faw-vw.com	/	会话	15						Medium



